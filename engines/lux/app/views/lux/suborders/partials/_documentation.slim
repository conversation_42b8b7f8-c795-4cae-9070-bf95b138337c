h3 Factura.
.div.gp-panel
  .row
    .small-12.columns
      h3 Utilice este espacio para gestionar carga y descarga de la factura del vehiculo reservado
  .row
    .small-6.columns
      = form_for(Mkp::Document.new, :url =>{:controller => :suborders ,:action => :upload_document }, :html => { :multipart => true }) do |form|
        = form.file_field :pdf_file, label: false, class: 'button'
        = form.select :pdf_file_type, @select_document_types, label: 'Tipo de documento'
        = form.hidden_field(:suborder_id, :value => @suborder.id)
        br
        - if @suborder.documents.any?
          - @suborder.documents.each do |x|
            = link_to x.pdf_file_type ,x.pdf_file.url, class: 'button grey', download: 'Factura_Mimoto'
            br
            br
        = form.submit 'Subir documentos', class: 'button red'
        br
        br
      - if @customer_reservation.present?
        = form_for(@customer_reservation, url: customer_reservation_purchase_path(nil,@customer_reservation) , method: :patch ) do |reservation_form|
          h3 Monto de Préstamo Máximo Disponible
          h3 = number_to_currency(@customer_reservation.maximum_lended_amount, precision: 0)
          br
          h3 Monto de Préstamo Aprobado
          h3 = number_to_currency(@customer_reservation.approved_lended_amount, precision: 0)
          h3 = reservation_form.label :approved_lended_amount, 'Modificar monto del Préstamo Aprobado por Sucursal (sin superar máximo disponible)'
          h3 = reservation_form.number_field :approved_lended_amount, within: 0..@customer_reservation.maximum_lended_amount, class: 'form-control no-arrows', value: nil, label: false, disabled: !current_user.is_bna_administrator? && !current_user.eshop_user?
          h3 = "(Solo modificable por Sucursal Bancaria)"
          = reservation_form.submit 'Actualizar' ,class: 'button red', disabled: !current_user.is_bna_administrator? && !current_user.eshop_user?
css class="no-arrows":
  input::-webkit-outer-spin-button{
    -webkit-appearance: none;
    margin: 0;
  }

  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type=number] {
    -moz-appearance: textfield;
  }