.large-3.columns
  = render partial: 'lux/partials/account_submenu'
.large-9.columns
  .gp-simple-panel
    = form_for @shop, url: shop_account_url, html: { id: 'edit_mkp_shop', class: 'custom'} do |f|
      .row
        .small-12.medium-6.columns
          - if @is_eshop_user
            = f.text_field :title, class: 'required', label: t('.title')
          - else
            = label_tag t('.title')
            %textarea{:disabled => true, :rows => 1}
              = @shop.title
      = f.text_area :description, size: '20x1', label: t('.description')
      = f.label t('.people')
      = text_field_tag 'shop_setting[notify_purchases]', @shop.setting.notify_purchases,
                                        placeholder: '<EMAIL>, <EMAIL>',
                                        title: 'Comma separated list of emails.',
                                        class: 'special_input',
                                        pattern: '\s*(\S+@\S+){1}(\s*,\s*\S+@\S+)*\s*'
      = f.label t('.public_name')
      - if @is_eshop_user
        = text_field_tag 'shop[public_name]', @shop.public_name
      - else
        %textarea{:disabled => true, :rows => 1}
          = @shop.public_name
      = f.label t('.public_email')
      = email_field_tag 'shop[public_email]', @shop.public_email,
                                              placeholder: '<EMAIL>',
                                              title: 'Solo se puede agregar un email'

      = f.label t('.phone')
      = text_field_tag 'shop[phone]', @shop.phone,
                                              placeholder: 'phone'

      = f.label t('.shop_web')
      = url_field_tag 'shop[web]', @shop.web,
                                  placeholder: 'https://www.example.com.ar'

      = f.label t('.account_number')
      = text_field_tag 'shop[account_number]', @shop.account_number, placeholder: 'account_number', readonly: 'readonly', disabled: 'disabled'

      %br

      - if @network == 'US'
        = f.label 'Customs Signer Name for International Shipment'
        = text_field_tag 'shop_setting[customs_signer_name]', @shop.setting.customs_signer_name,
                                             title: 'Name of the Signer.',
                                             class: "#{'required' if @shop.has_international_shipping_method?}"

      - if @is_eshop_user
        %h4= t('.visible')
        .row
          .small-4.columns
            .switch.round
              %input{ id: 'visible_off', name: 'shop[visible]', type: 'radio', checked: !@shop.visible, value: 'false' }
              %label{ for: 'visible_off', onclick: '' } No
              %input{ id: 'visible_on', name: 'shop[visible]', type: 'radio', checked: @shop.visible, value: 'true' }
              %label{ for: 'visible_on', onclick: '' }= t('.yes_active')
              %span
      %br
      .row
        .small-12.columns
          %button.button.red.small{ type: 'submit' }= t('.update')
