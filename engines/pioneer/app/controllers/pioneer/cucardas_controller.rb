module Pioneer
  class CucardasController < Pioneer::ApplicationController
    before_action :set_cucarda, only: [:show, :edit, :update, :destroy, :update_cucarda_active]
    before_action :set_current_store, only: [:update_cucarda_active]
    layout 'bootstrap_layout'

    def index
      if params[:search]
        @cucardas = Cucarda.where("name LIKE ? OR descripcion LIKE ?", "%#{params[:search]}%", "%#{params[:search]}%").order(name: :asc).paginate(page: params[:page])
      else
        @cucardas = Cucarda.order(name: :asc).paginate(page: params[:page])
      end
    end

    def show
    end

    def new
      @cucarda = Cucarda.new
    end

    def edit
    end

    def create
      @cucarda = Cucarda.new(cucarda_params)

      if @cucarda.save
        redirect_to @cucarda, notice: 'La cucarda fue creada exitosamente.'
      else
        render :new
      end
    end

    def update
      if cucarda_params[:cucarda_active].present?
        cucarda_active_value = ActiveModel::Type::Boolean.new.cast(cucarda_params[:cucarda_active])
        @cucarda.cucarda_active = cucarda_active_value
      end

      if @cucarda.update(cucarda_params.except(:cucarda_active))
        redirect_to @cucarda, notice: 'La cucarda fue actualizada exitosamente.'
      else
        render :edit
      end
    end

    def update_cucarda_active
      if params[:cucarda_active].present?
        cucarda_active_value = params[:cucarda_active]
      
        @cucarda.stores = JSON.parse(@cucarda.stores.to_json || "[]")
        if cucarda_active_value == 'true'
          @cucarda.stores = @cucarda.stores + [@store.name] unless @cucarda.stores.include?(@store.name)
        else
          @cucarda.stores = @cucarda.stores - [@store.name]
        end
    
        if @cucarda.save
          redirect_to @cucarda, notice: 'La cucarda fue actualizada exitosamente.' and return
        else
          render json: { status: 'error', message: @cucarda.errors.full_messages } and return
        end
      else
        render :edit
      end
    end

    def destroy
      @cucarda.destroy!

      redirect_to cucardas_url, notice: 'La cucarda fue borrada exitosamente.'
    end

    private

    def set_cucarda
      @cucarda = Cucarda.find(params[:id])
    end

    def set_current_store
      @store = Mkp::Store.find(params[:store_id])
    end

    def cucarda_params
      params.require(:cucarda).permit(:name, :descripcion, :image)
    end
  end
end
