.py-4.mt-4.border-top
  %h4= t('pioneer.cucardas.config')
  .row.my-4
    .col-xs-6
      = form.collection_select :cucarda_id,
                         [[t('pioneer.cucardas.select'), nil]] + Cucarda.all.map { |c| [c.name, c.id] },
                         :last,
                         :first,
                         { include_blank: false, label: t('pioneer.cucardas.title') },
                         { class: "form-control", id: "mkp_category_cucarda_id" }  # Asegúrate de que el id sea correcto

    .col-xs-6
      .checkbox
        = form.label :cucarda_active, t('pioneer.cucardas.active') do
          = form.check_box :cucarda_active, class: "cucarda-active-toggle", data: { url: update_cucarda_stores_categories_path }

:javascript
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded event fired');
    $(document).on('change', '.cucarda-active-toggle', function() {
      var cucardaActive = this.checked;
      var cucardaId = $('#mkp_category_cucarda_id').val(); 
      var categoryId = window.location.pathname.split('/').filter(function(part) {
        return part.match(/^\d+$/);
      }).pop();
      var url = $(this).data('url');
      var storeId = new URLSearchParams(window.location.search).get('store_id'); 
      $.ajax({
        type: 'PATCH',
        url: url,
        data: { cucarda_id: cucardaId, cucarda_active: cucardaActive, store_id: storeId, categoryId: categoryId },
        
      });
    });
  });