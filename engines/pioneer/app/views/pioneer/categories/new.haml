- installments = (1..12).to_a | [15,18,24,36,50]
= content_for :title do
  - if @category.new_record?
    = "Pioneer - Nueva Categoría"
  - else
    = "Pioneer - #{@category.name}"

- length_units = Mkp::Unit::Length.all.map { |measure| measure }.unshift("")
- mass_units = Mkp::Unit::Mass.all.map { |measure| measure }.unshift("")
.row.my-4
  .col-xs-12
    - if @category.new_record?
      - if @parent_category
        %h3= "#{t('pioneer.categories.cat-sub')} <i>#{@parent_category.name}</i>"
      - else
        %h3= t('pioneer.categories.cat-main')
    - else
      %h3= "#{t('pioneer.categories.cat-edit')} - #{@category.name}"
.row.my-4
  .col-xs-12
    - url = @category.new_record? ? categories_path : category_path(id: @category.id)
    = form_for @category, url: url, multipart: true do |f|
      - if @parent_category
        = hidden_field_tag :parent_id, @parent_category.id
      .row.my-4
        .col-xs-6
          = f.label :name, t('pioneer.categories.name')
          = f.text_field :name, class: 'form-control', label: false
        .col-xs-6
          - if @category.new_record?
            - if create_category_enable_checkbox?(@parent_category)
              .checkbox
                = f.check_box :active, { label: t('pioneer.categories.active')}
            - else
              %p
                %i The parent is invisible so it's not possible to change this at this level
          - else
            - if edit_category_enable_checkbox?(@category)
              .checkbox
                = f.check_box :active, { label: t('pioneer.categories.active')}
            - else
              .pt-5.text-danger= t('pioneer.categories.visibility-error')
      .row.my-4
        -if @category.new_record?
          .col-xs-12
            = f.label :description, t('pioneer.categories.description')
            = f.text_area :description, maxlength: 250, class: 'form-control', label: false
        - else
          .col-xs-6
            = f.label :description, t('pioneer.categories.description')
            = f.text_area :description, maxlength: 250, class: 'form-control', label: false
          .col-xs-6
            = f.select :pickup, Mkp::Category.pickups_for_select, { include_blank: false, label: t('pioneer.categories.pickeable')}, {class: "form-control"}

      .row.my-5
        .col-xs-12
          %h4= t('pioneer.categories.cat-default')
          .row.my-3
            .col-xs-6
              = f.label :weight, t('pioneer.categories.weight')
              = f.text_field :weight,class:'form-control',label:false
            .col-xs-6
              = f.select :mass_unit, mass_units, {include_blank: false, label: t('pioneer.categories.mass-unit')}, {class: "form-control"}

          .row.my-3
            .col-xs-6
              = f.label :height, t('pioneer.categories.height')
              = f.text_field :height, class:'form-control', label: false
            .col-xs-6
              = f.label :length, t('pioneer.categories.length')
              = f.text_field :length, class:'form-control', label: false
          .row.my-3
            .col-xs-6
              = f.label :width, t('pioneer.categories.width')
              = f.text_field :width, class:'form-control', label: false
            .col-xs-6
              = f.select :length_unit, length_units, {include_blank: false, label: t('pioneer.categories.length-unit')}, {class: "form-control"}

      = render partial: 'pioneer/shared/cucarda', locals: { form: f }

      .py-4.mt-4.border-top
        %h4= t('pioneer.categories.cat-visa-points')
        .row.my-4
          .col-sm-12.col-md-6
            .form-group
              = f.number_field :visa_puntos_equivalence, step: '0.000001', max: 10, min: 0.000001, class: 'form-control', label: t('pioneer.categories.cat-visa-equivalence')

      - if @stores.present?
        %h4= t('pioneer.categories.stores')
        - @stores.each do |store|
          .row.py-4.my-4.border-bottom

            .col-xs-12.col-md-2
              .checkbox
                = f.label "mkp_category[store_ids][]" do
                  = check_box_tag "mkp_category[store_ids][]", store.id, @category.stores.include?(store)
                  = store.name
            .col-xs-12.col-md-10
              .row.my-4
                .col-xs-12.col-md-3
                  = label "mkp_category[installments][#{store.id}]", t('pioneer.categories.cat-max-installment')
                  = select_tag "mkp_category[installments][#{store.id}]", options_for_select(installments, @category.category_stores.find_by_store_id(store.id).try(:installments).to_i), {class:'form-control'}
                .col-xs-12.col-md-3
                  = label "mkp_category[commission][#{store.id}]", t('pioneer.categories.commission')
                  = number_field_tag "mkp_category[commission][#{store.id}]", @category.category_stores.find_by_store_id(store.id).try(:commission), {step: '0.01', max: 100, min: 0.01, class: 'form-control'}
                .col-xs-12.col-md-5
                  - if (cover = @category.category_stores.find_by_store_id(store.id).try(:cover)).present?
                    = image_tag cover.url(:t)
                    .pt-4
                      = link_to 'Delete Photo', category_remove_photo_path(id: cover.id, category_id: @category.id), confirm: 'Are you sure?', class: 'button tiny alert'
                  - else
                    =f.label t('pioneer.categories.cat-store-banner', store: store.name)
                    = file_field_tag "mkp_category[store_id_pictures][#{store.id}][]", { class:'form-control'}
            .col-xs-12.col-md-2
            .col-xs-12.col-md-10
              .row.my-4
                .col-xs-12.col-md-3
                  = label "mkp_category[insurance_coef][#{store.id}]", t('pioneer.categories.insurance_coef')
                  = number_field_tag "mkp_category[insurance_coef][#{store.id}]", @category.category_stores.find_by_store_id(store.id).try(:insurance_coef), {step: '0.01', max: 100, min: 0.01, class: 'form-control'}
                .col-xs-12.col-md-3
                  = label "mkp_category[insurance_start_date][#{store.id}]", t('pioneer.categories.insurance_start_date')
                  = date_field_tag "mkp_category[insurance_start_date][#{store.id}]", @category.category_stores.find_by_store_id(store.id).try(:insurance_start_date)&.strftime('%Y-%m-%d'), { min: Date.today, class: 'form-control'}
                .col-xs-12.col-md-3
                  = label "mkp_category[insurance_end_date][#{store.id}]", t('pioneer.categories.insurance_end_date')
                  = date_field_tag "mkp_category[insurance_end_date][#{store.id}]", @category.category_stores.find_by_store_id(store.id).try(:insurance_end_date)&.strftime('%Y-%m-%d'), { min: Date.today, class: 'form-control'}
                .col-xs-12.col-md-3.mt-5
                  = check_box_tag "mkp_category[active_insurance][#{store.id}]", true, @category.category_stores.find_by_store_id(store.id).try(:active_insurance)
                  = 'Activar Seguros'
      .text-right.my-5
        %button.btn.btn-primary= t('pioneer.categories.save')
        = hidden_field_tag :store_id,  params[:store_id] if  params[:store_id].present?
