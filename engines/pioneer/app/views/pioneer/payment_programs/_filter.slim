= form_tag '', method: :get, authenticity_token: false, class: 'form-inline'
  select.form-control id="go-to-store" name="store_id"
    option value="" No store
    - Mkp::Store.find_each do |store|
      - if current_admin.is_store_owner?(store)
        option value="#{store.id}" selected=(params[:store_id].to_i == store.id) = store.name.titleize
  button.btn.btn-success.ml-2 type='submit'
    = t('pioneer.payment_programs.submit')

