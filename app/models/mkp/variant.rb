module Mkp
  class Variant < ActiveRecord::Base
    APPLY_TO_PENDING = %W[sku gp_sku ean_code properties discount_top points_price].freeze

    has_paper_trail
    include Concerns::SoftDestroy
    include Concerns::GenerateVariantSku

    belongs_to :picture, class_name: 'Attachment::ProductPicture'
    belongs_to :product, unscoped: true
    belongs_to :shop

    has_one :currency, through: :product

    has_many :pictures, class_name: 'Attachment::ProductPicture'
    has_many :cart_items, dependent: :destroy
    has_many :order_items
    has_many :costs, class_name: "VariantCost", dependent: :destroy
    has_and_belongs_to_many :wishlists

    has_many :external_objects, class_name: 'Integration::Object', as: :integrable, dependent: :destroy

    validates :quantity, presence: true, numericality: { greater_than_or_equal_to: 0 }

    after_create :associate_with_shop

    after_update :apply_for_status_change

    after_create :set_to_pending
    after_destroy :set_to_pending

    after_save :set_visible_if_pictures

    scope :by_network,          ->(network) { joins(:shop).merge(Shop.by_network(network)) }
    scope :by_manufacturer,     ->(manufacturer_id) { joins(:product).where('mkp_products.manufacturer_id = ?', manufacturer_id) }
    scope :by_shop_in_network,  ->(shop_id, network) { joins(:shop).where('mkp_shops.slug = ? and mkp_shops.network = ?', shop_id, network) }
    scope :exclude_shops,       ->(shop_ids) { joins(:shop).where('mkp_shops.id NOT IN (?)', shop_ids) }
    scope :newest,              ->(count) { order('mkp_variants.created_at DESC').limit(count) }
    scope :newest_random,       ->(count) { where('mkp_variants.created_at > ?', Date.today.prev_month(3)).order('RAND()').limit(count) }
    scope :random,              ->(number) { order('RAND()').limit(number) }
    scope :with_pictures,       -> { where('mkp_variants.picture_id IS NOT NULL') }
    scope :with_stock,          -> { where('mkp_variants.quantity > 0 AND mkp_variants.quantity > mkp_variants.reserved_quantity') }
    scope :on_sale,             -> { joins(:product).merge(Product.on_sale) }
    scope :available,           -> { joins(:product).merge(Product.available) }
    scope :unavailable,         -> { joins(:product).merge(Product.unavailable) }
    scope :not_deleted,         -> { joins(:product).merge(Product.not_deleted) }
    scope :visibles,            -> { where('mkp_variants.visible = ?', true) }
    scope :visible_shop,        -> { joins(:shop).where('mkp_shops.visible = ?', true) }
    scope :active,              -> { available.not_deleted.visible_shop }
    scope :with_sku,            -> { where('sku != ?', '') }
    scope :gp_sku_like,         ->(query) { where("gp_sku LIKE ?", "#{sanitize_sql_like(query)}%") }

    def self.product_sku(product_id, sku)
      sku = [nil, sku].uniq if sku.empty?
      where(product_id: product_id, sku: sku)
    end

    default_scope { where('mkp_variants.deleted_at IS NULL') }

    serialize :properties, Hash

    delegate :title, :currency_symbol, :description, :network, :price, :shop, :sale_fields_not_null?, :purchasable, :reservable, :discount_percentage, :stock,
             to: :product

    searchable do
      text :title

      text :description

      text :manufacturer_name do
        product.manufacturer.name
      end

      text :category_name do
        categories.map(&:name).join(' ')
      end

      text :color_name do
        has_property?(:color) && color_name
      end

      string :category do
        category.name.to_s
      end

      integer :id

      integer :quantity

      integer :price, trie: true

      time :created_at, trie: true

      string :color_hex do
        if properties[:color].present? && properties[:color].is_a?(Hash) && properties[:color][:hex].present?
          Mkp::ProductColors.generic_hex_for(properties[:color][:hex])
        end
      end

      string :size_name

      string :custom_properties, multiple: true do
        ret = []
        custom_properties&.each_pair do |key, value|
          if key == :cilindrada && value.present?
            case value.to_s.gsub(/cc/i, '').to_i
            when 0..299
              value = "baja cilindrada"
            when 300..750
              value = "media cilindrada"
            else
              value = "alta cilindrada"
            end
          end
          ret << "#{key}-#{value}".downcase if value.present?
        end
        ret
      end

      string :available_sizes, multiple: true do
        product.variants.with_stock.flat_map do |v|
          v.properties[:size].downcase if v.properties[:size].present?
        end.uniq.compact
      end

      string :network do
        shop.network
      end

      integer :shop_order_visibility do
        shop.order_visibility || 0
      end

      integer :product_order_visibility do
       product.product_stores.compact.map(&:order_visibility).max || 0
      end

      text :shop_name do
        product.shop.name.downcase
      end

      text :data do
        product.data.map{ |d| d.second[0][:value].downcase }.uniq.compact.join
      end

      integer :sports_ids, multiple: true, references: Sport do
        product.sports.map(&:id)
      end

      integer :store_id, multiple: true, references: Store do
        category.store_ids & product.shop.store_ids
      end

      integer :rejected_for_store_ids, multiple: true, references: Store do
        product.product_stores.rejected.map(&:store_id)
      end

      integer :pending_for_store_ids, multiple: true, references: Store do
        product.product_stores.pending.map(&:store_id)
      end

      integer :approved_for_store_ids, multiple: true, references: Store do
        product.product_stores.approved.map(&:store_id)
      end

      integer :product_id, references: Product

      string :product_id_str do
        product_id.to_s
      end

      integer :manufacturer_id, references: Manufacturer do
        product.manufacturer_id
      end

      string :manufacturer_id_str do
        product.manufacturer_id.to_s
      end

      integer :shop_id, references: Shop do
        shop_id
      end

      string :shop_id_str do
        shop_id.to_s
      end

      string :manufacturer_friendly_id, multiple: true do
        m_id = product.manufacturer_id
        m_slugs = FriendlyId::Slug.where(sluggable_type: Mkp::Manufacturer)
                                  .where(sluggable_id: m_id)
                                  .pluck(:slug)
        [m_id.to_s].concat m_slugs
      end

      integer :genders_ids, multiple: true, references: Gender do
        product.genders.flat_map(&:path_ids).uniq
      end

      string :genders_friendly_ids, multiple: true do
        product.genders.flat_map(&:path).uniq.map { |g| [g.id.to_s].concat(g.slugs.pluck(:slug)) }.flatten
      end

      integer :categories_ids, multiple: true, references: Category do
        categories_ids
      end

      string :categories_friendly_ids, multiple: true do
        categories_ids.map(&:to_s).concat Mkp::Category.get_full_path_slugs_from(categories)
      end

      boolean :deleted do
        deleted_at? || product.deleted_at?
      end

      boolean :display_variant do
        visible
      end

      boolean :shop_visible do
        shop.visible
      end

      time :sale_on, trie: true do
        product.sale_on
      end

      time :sale_until, trie: true do
        product.sale_until
      end

      boolean :sale_fields_are_present do
        sale_fields_not_null?
      end

      boolean :shop_deleted do
        shop.deleted_at.present?
      end

      boolean :purchasable do
        purchasable
      end

      boolean :reservable do
        reservable
      end

      string :transaction_type do
        product.transaction_type
      end

      time :shop_massive_discount_on, trie: true do
        shop.setting.discount_starts_at
      end

      time :shop_massive_discount_until, trie: true do
        shop.setting.discount_ends_at
      end

      time :available_on, trie: true do
        product.available_on
      end

      string :shop_friendly_id, multiple: true do
        [shop.id.to_s, shop.slug]
      end

      string :available_countries, multiple: true do
        if shop.zones.map(&:countries).any?(&:empty?)
          ['all']
        else
          shop.zones.flat_map(&:countries).uniq
        end
      end

      time :last_sold_at, trie: true do
        order_items.order('created_at DESC').limit(1).pluck(:created_at).first
      end

      integer :sold_count, trie: true  do
        order_items.to_a.sum(&:quantity)
      end

      text :gp_sku
    end

    def external_object(integration_name)
      external_objects.detect{ |object| object.integration_name == integration_name }
    end

    def self.with_property(prop_name, prop_value)
      all.select do |v|
        v.properties[prop_name] && v.properties[prop_name] == prop_value
      end
    end

    def self.everything
      scoped.tap { |relation| relation.default_scoped = false }
    end

    def quantity
      return super if reserved_quantity == 0
      available_quantity
    end

    def category
      return product.category if product
      nil
    end

    def categories
      category.try(:path) || []
    end

    def categories_ids
      category.try(:path_ids) || []
    end

    def color_name
      properties[:color][:name] if has_property?(:color) && properties[:color].is_a?(Hash)
    end

    def color_slug_name
      properties[:color][:name].parameterize('') if has_property?(:color) && properties[:color].is_a?(Hash)
    end

    def color_hex
      properties[:color][:hex] if has_property?(:color) && properties[:color].is_a?(Hash)
    end

    def size_name
      properties[:size].downcase if has_property?(:size)
    end

    def custom_properties
      ret = {}
      product.available_properties_names.each_pair do |key, value|
        ret[value.to_sym] = properties[key.to_sym] if value.present?
      end
      ret
    end

    def show
      update_attributes(visible: true)
    end

    def hide
      update_attributes(visible: false)
    end

    def decrease_quantity(summand)
      changes = {
        quantity: (attributes['quantity'] >= summand) ? attributes['quantity'] - summand : 0
      }.tap do |hash|
        if (@recalculate_visibility = hash[:quantity].zero? && visible)
          hash[:visible] = false
        end
      end

      update_attributes!(changes)

      VariantVisibilityWorker.perform_async(id) if @recalculate_visibility
    end

    def increase_quantity(summand)
      changes = {
        quantity: attributes['quantity'] + summand
      }
      update_attributes(changes)
    end

    def decrease_reserved_quantity(summand)
      changes = {
        reserved_quantity: (reserved_quantity >= summand) ? reserved_quantity - summand : 0
      }.tap do |hash|
        available = attributes['quantity'] - hash[:reserved_quantity]

        if (@recalculate_visibility = available.zero? && visible)
          hash[:visible] = false
        elsif available > 0 && can_become_visible?
          hash[:visible] = true
        end
      end

      update_attributes(changes)

      VariantVisibilityWorker.perform_async(id) if @recalculate_visibility
    end

    def increase_reserved_quantity(summand)
      changes = {
        reserved_quantity: reserved_quantity + summand
      }.tap do |hash|
        available = attributes['quantity'] - hash[:reserved_quantity]

        if (@recalculate_visibility = available.zero? && visible)
          hash[:visible] = false
        elsif available > 0 && can_become_visible?
          hash[:visible] = true
        end
      end

      update_attributes(changes)

      VariantVisibilityWorker.perform_async(id) if @recalculate_visibility && product.available?
    end

    def get_thumb(size = :ml)
      if picture.present? && picture.photo_file_name.present?
        picture.url(size)
      elsif product.pictures.present? && product.pictures.first.photo_file_name.present?
        product.pictures.first.url(size)
      end
    end

    def get_url(options = {})
      if has_property?(:color) && properties[:color].is_a?(Hash)
        options[:color] = properties[:color][:name]
      end
      product.get_url(options)
    end

    def has_property?(prop_name)
      properties[prop_name].present?
    end

    def picture
      picture_id.nil? ? product.picture : super
    end

    def set_visible_if_pictures
      picture.present? ? update_columns(visible: true) : update_columns(visible: false)
    end

    # Get property name based on position
    # Needed for exporting products
    def property_name(position)
      properties.to_a[position - 1].try(:fetch, 0)
    end

    # Get property value based on position
    # Needed for exporting products
    def property_value(position)
      value = properties.to_a[position - 1].try(:fetch, 1)
      return value if value.is_a?(String)
      return value[:name] if value.is_a?(Hash)
    end

    def name
      properties.values.map do |value|
        value.is_a?(Hash) ? value[:name] : value
      end.join("-")
    end

    def has_stock?
      quantity > 0
    end

    def smart_sku
      gp_sku || sku || ""
    end

    def current_cost(date_time=nil)
      date_time = Time.now unless date_time.present?
      costs = self.costs.where(':date >= date_from AND :date <= date_to', date: date_time).last.try(:cost) if self.costs.present?
    end

    def apply_for_status_change
      if self.changes.keys.any?{ |k| APPLY_TO_PENDING.include?(k) }
        set_to_pending
      end
     end

    def set_to_pending
      product.product_stores.each{ |ps| ps.update(status: 0) }
    end

    def enabled_for_store?(store)
      shop.stores.exists?(mkp_shop_stores: {store_id: store.id, active: true})
    end

    private

    def available_quantity
      real_quantity = attributes['quantity'] - reserved_quantity
      if real_quantity > 0
        return real_quantity
      else
        return 0
      end
    end

    def associate_with_shop
      update_column(:shop_id, product.shop_id)
    end

    def soft_destroy?
      order_items.present?
    end

    def can_become_visible?
      (not visible) && (not product.available_siblings_for_variant(self).any?(&:visible)) && product.available?
    end
  end
end
