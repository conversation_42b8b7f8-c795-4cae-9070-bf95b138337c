module Mkp
  class Suborder < ActiveRecord::Base
    has_paper_trail

    serialize :data, Hash

    belongs_to :coupon, class_name: '::Mkp::Coupon::Shop'
    belongs_to :order
    belongs_to :shop
    belongs_to :office, class_name: 'Mkp::Bna::Office'

    has_many :items, class_name: 'OrderItem', dependent: :destroy
    has_many :payments, through: :items, dependent: :destroy
    has_many :products, through: :items
    has_many :shipments, dependent: :destroy
    has_many :invoice_items, as: :suborder, dependent: :destroy
    has_many :documents, dependent: :destroy, inverse_of: 'suborder'
    has_many :warranty_responses, dependent: :destroy
    validates :purchase_id, uniqueness: true

    delegate :customer, :network, :payment, :coupon, :coupon_discount, to: :order, allow_nil: true


    before_create :set_purchase_id


    STATUS = %w[returned exchange cancelled fulfilled not_delivered in_process delivered
              booked approved declined billed posted pending_cancellation pending overdue].freeze
    BNA_STATES_FLOW = {
        'booked' => %w[approved cancelled overdue],
        'approved' => %w[billed pending_cancellation],
        'cancelled' => [],
        'declined' => [],
        'billed' => %w[posted],
        'delivered' => [],
        'posted' => %w[delivered],
        'pending_cancellation' => %w[approved declined],
        'overdue' => []
    }

    PICKUP_STATES_FLOW = {
      'unfulfilled' => %w[ready_to_pick_up],
      'ready_to_pick_up' => %w[delivered]
    }

    STATES_FLOW = {
      'bna' => BNA_STATES_FLOW,
      'pickup' => PICKUP_STATES_FLOW
    }

    RESERVABLE_STORES_IDS = [43, 35]

    PICKABLE_STATUS = %w[unfulfilled delivered ready_to_pick_up]

    scope :reservables, -> { includes(:order).where(mkp_orders: {store_id: RESERVABLE_STORES_IDS}).distinct }

    searchable do
      boolean :external_redemption_completed
      integer :product_ids, multiple: true, references: Product
      integer :id
      integer :shop_id
      string :logistic_type
      string :select_status_product
      string :status
      text :title
      text :public_id
      time :created_at, trie: true
      integer :office_id

      text :order_title do
        order.title
      end

      text :shop_name do
        shop.title if shop.present?
      end

      text :gp_sku do
        items.map(&:variant).map(&:gp_sku).join(', ')
      end

      text :customer_data do
        [customer.full_name, customer.email].join(', ') if customer.present?
      end

      string :customer_email do
        customer.email if customer.present?
      end

      text :gp_sku do
        items.map(&:variant).map(&:gp_sku).join(' ')
      end

      text :public_ids_data do
        order.suborders.map(&:public_id).join(' ')
      end

      string :tracking_numbers, multiple: true do
        shipments.flat_map { |s| s.labels.active }.map(&:tracking_number).uniq.compact
      end

      string :network, stored: true do
        shop.network if shop.present?
      end

      boolean :fulfilled do
        fulfilled_by_gp?
      end

      time :paid_at, trie: true do
        if payment.present?
          payment.collected_at if payment.collected?
        else
          created_at unless order.balance_due?
        end
      end

      string :order_item_status, multiple: true do
        items.map(&:status)
      end

      string :payment_status, multiple: true do
        order.payment_status(suborder: self)
      end

      string :shipment_status do
        shipments.map(&:status).last
      end
    end

    %w[in_process? cancelled? fulfilled? unfulfilled?].each do |state|
      define_method(state) { status == state.delete('?') }
    end

    %i[length width height].each do |attr|
      define_method("get_suborder_#{attr}") do
        items.inject(0) do |result, item|
          total = item.product.send("#{attr}_with_unit").to.centimeters.value * item[:quantity]
          total = 2 unless total > 0.0
          result += total
        end.round(2)
      end
    end

    def office_finder
      office_id if office_id.present?
    end

    def charged_shipment?
      return false unless has_shipments?
      return false if has_shipment_bonification?

      true
    end

    def coupon_discount
      self[:coupon_discount] || 0
    end

    def encryptor_initialization_vector
      Digest::SHA256.hexdigest(purchase_id)
    end

    def encryptor_key
      ::EXTERNAL_COMMUNICATIONS_KEY
    end

    def exchange?
      data.key? :exchange
    end

    def fulfilled_by_gp?
      fulfilled_by_gp || is_the_network_fulfiller?
    end

    def get_suborder_weight
      items.inject(0) do |result, item|
        weight = item.product.weight_with_unit.to.kilograms.value * item[:quantity]
        weight = 2 unless weight > 0.0
        result += weight
      end
    end

    def has_coupon?
      coupon_discount.present? && !!coupon_id
    end

    def has_shipments?
      shipments.present?
    end

    def has_shipment_bonification?
      shipments.detect(&:has_bonification?)
    end

    def have_taxes?
      taxes > 0
    end

    def is_cancellable?
      return false unless order.store.allow_orders_cancellation
      return false unless order.payments.all? { |p| p.status == 'collected' } && !refunded?
      return false unless shipments.any?
      return true if order.suborders.detect { |s| s.warranty? }

      cancellable_statuses = %w[unfulfilled in_process not_delivered pending]
      shipments.all? { |s| cancellable_statuses.include?(s.status) }
    end

    def is_refundable?
      shop.is_refundable?(self) if shop.present?
    end

    def is_refundable_by_suborder?
      return false unless shipments.any?

      refundable_statuses = %w[delivered]
      shipments.all? do |s|
        refundable_statuses.include?(s.status) || (s.not_delivered? && s.shipment_kind_label != 'Envio')
      end && payments.all? { |p| p.status == 'collected' } && products.all?(&:is_refundable?)
    end

    def all_payments
      payments.to_a + order.payments.to_a
    end

    def is_refundable_by_product?
      items.any?(&:is_refundable?) && products.all?(&:is_refundable?)
    end

    def public_id
      "#{order_id}-#{id}-#{shop_id}"
    end

    def shipment
      shipments.last
    end

    def shipment_cost
      shipment.try(:charged_amount) || 0
    end

    def self.available_statuses
      STATUS.map { |state| [state.titleize, state] }
    end

    delegate :name, to: :store, prefix: true

    delegate :name, to: :shop, prefix: true

    def status
      shipments_status = shipments.map(&:status).uniq
      return items.first&.status || '-' if shipments_status.blank?

      result = shipments_status.first if shipments_status.count == 1

      status_by_shipment(result, shipments_status) || 'in_process'
    end

    def status_by_shipment(result, shipments_status)
      return 'cancelled' if canceled_shipments?(result)
      return 'not_delivered' if result == 'not_delivered'
      return 'returned' if returned_shipments?(result)
      return 'exchange' if exchange_shipments?(result)
      return 'returned' if refunded && delivered_and_refunded_shipment?
      return 'fulfilled' if fulfilled_shipments?(result)
      return 'unfulfilled' if shipments_status.include?('unfulfilled')
      return 'ready_to_pick_up' if shipments_status.include?('ready_to_pick_up')
    end

    def canceled_shipments?(result)
      result == 'cancelled' && canceled_items?
    end

    def exchange_shipments?(result)
      result == 'delivered' && exchange?
    end

    def returned_shipments?(result)
      result == 'delivered' && refunded && refunded_shipments?
    end

    def fulfilled_shipments?(result)
      return true if result == 'delivered' && virtual_delivery?

      shipments.select { |s| s.shipment_kind_label == 'Envio' }
               .all?(&:delivered?) && shipments.reject { |s| s.shipment_kind_label == 'Envio' }
                                               .all?(&:not_delivered?)
    end

    def virtual_delivery?
      shipments.map(&:shipment_kind_label).uniq == ['Virtual']
    end

    def delivered_and_refunded_shipment?
      shipments.any? { |s| s.status == 'delivered' && s.shipment_kind == 'refund' }
    end

    def canceled_items?
      items.all? { |i| i.status == 'cancelled' }
    end

    def refunded_shipments?
      shipments.any? { |s| s.shipment_kind == 'refund' }
    end

    def logistic_type
      last_shipment = shipments.map(&:shipment_kind).last
      kind = { 'exchange_refund' => 'exchange', 'exchange_change' => 'exchange', 'refund' => 'refund', 'pickup' => 'pickup' }
      return kind[last_shipment] unless kind[last_shipment].nil?

      'normal'
    end

    def select_status_product
      operation = shipments.map(&:shipment_kind).last
      shipment_status = shipments.map(&:status).last
      case operation
      when 'refund'
        if shipment_status == 'delivered'
          shipments.where(status: 'delivered').map(&:status).last
        else
          shipments.where.not(status: 'delivered').map(&:status).last
        end
      when 'exchange'
        shipments.map(&:status).last
      else
        shipment_status
        end
    end

    def store
      order.try(:store)
    end

    def subtotal
      items.to_a.sum(&:total)
    end

    def subtotal_points
      items.to_a.sum(&:points)
    end

    def taxes
      super || 0
    end

    def total
      total_without_discount - coupon_discount.to_f
    end

    def total_points_price
      items.to_a.sum(&:point_price)
    end

    def point_equivalent
      items.first.point_equivalent
    end

    def iva
      items.first.iva
    end

    def total_points
      t_points = items.sum(:points)
      return t_points if t_points != 0
      points_payment = all_payments.filter {|p| p.gateway == "LoyaltyBna"}.first
      return unless points_payment.present?
      points_gateway = points_payment.get_subpayment_site_id(self, self.shop)
      points_gateway&.dig("points")&.to_i || 0
    end

    def total_products
      items.sum(:quantity)
    end

    def total_without_discount
      total_without_shipment + shipment_cost
    end

    def total_without_shipment
      subtotal + taxes
    end

    def total_without_points
      items.map(&:total_without_points).sum
    end

    def total_price_charged
      items.map(&:unit_price_charged).sum
    end

    def shipment_delivered
      if refunded
        payments.where(gateway: 'VisaPuntos').find_each do |payment|
          payment.cancel!(store)
        end
      end
    end


    ######### BNA Reservable States develop (start) #########

    def is_reservable?
      RESERVABLE_STORES_IDS.include? store.id
    end

    def can_show_reservable_states?
      [43, 35].include? store.id
    end

    def state_possible_actions(type)
      available_actions = STATES_FLOW[type][status]
      if self.store.id == 43 # Solo reservas BNA móvil
        available_actions
      else
        available_actions - ["overdue"] # Elimina "overdue" para no-reservas
      end
    end

    def actions_for_current_user(current_user)
      if current_user.eshop_user?
        state_possible_actions("bna")
      elsif current_user.movilidad_superuser?
        return  %w[approved cancelled]
      elsif current_user.office.present?
        return  %w[approved declined posted cancelled]
      else
        return  %w[delivered pending_cancellation billed]
      end
    end

    def state_display_buttons(type)
      STATES_FLOW[type][status]
        .reject { |available_status| available_status == 'overdue' }
        .map do |available_status|
          {
            id: available_status,
            label: available_status
          }
        end
    end

    def apply_state_change(next_status)
      PaperTrail.request.whodunnit = 1 unless PaperTrail.request.whodunnit.present?
      self.paper_trail_event = "movilidad_state_change_#{next_status}"
      touch
      items.update_all(status: next_status)
      if store.id == 43 # BNA
        case next_status
        when 'overdue'
          # enviar posible notificación de reserva vencida
          remove_reservation
        when 'cancelled'
          Avenida::Emblue::Mailer::NotifyEvent.new(order.id).notify_cancelled_order
          remove_reservation
        when 'approved'
          Avenida::Emblue::Mailer::NotifyEvent.new(order.id).notify_approved_order
        when 'declined'
          Avenida::Emblue::Mailer::NotifyEvent.new(order.id).notify_declined_order
        when 'billed'
          Avenida::Emblue::Mailer::NotifyEvent.new(order.id).notify_billed_order
        when 'posted'
          update_column(:posted_at, Time.current)
          Avenida::Emblue::Mailer::NotifyEvent.new(order.id).notify_posted_order
        when 'delivered'
          Avenida::Emblue::Mailer::NotifyEvent.new(order.id).notify_delivered_order
        when 'pending_cancellation'
          Avenida::Emblue::Mailer::NotifyEvent.new(order.id).notify_pending_cancelation
        else
        end
      end
    end

    def remove_reservation
      Mkp::Bna::RemoveCustomerReservationWorker.new.perform(order.id)
    end
    ######### BNA Reservable States develop (end) #########

    def warranty?
      shop_id == OCHENTA_SHOP_ID
    end

    def sub_payment_for_suborder
      if avenida_decidir_distributed_gateway?
        sub_payments = order.payments.flat_map { |payment| payment&.gateway_data&.[]('gateway')&.[]('sub_payments') || []}
        sub_payments.find { |sp| sp['site_id'] == shop.decidir_site_id }
      elsif decidir_distributed_gateway?
        sub_payments = order.payments.flat_map { |payment| payment&.gateway_data&.[]('sub_payments') || []}
        sub_payments.find { |sp| sp['site_id'] == shop.decidir_site_id }
      elsif modo_gateway?
        payment_data = payment&.gateway_data
        if payment_data && payment_data[:response] && payment_data[:response][:sub_payments] && payment_data[:response][:sub_payments].any?
          payment_data[:response][:sub_payments].first
        end
      end
    end

    def gateway_exists?(gateway)
      gateway_values = order.payments.map(&:gateway)
      gateway_values.include?(gateway)
    end

    def decidir_distributed_gateway?
      gateway_exists?("DecidirDistributed")
    end

    def avenida_decidir_distributed_gateway?
      gateway_exists?("AvenidaDecidirDistributed")
    end

    def modo_gateway?
      gateway_exists?("AvenidaModoDistributed") || gateway_exists?("AvenidaModo")
    end

    def payment_status
      order.payment_status(suborder: self)
    end

    private

    def credit_note_generate
      return unless refunded?

      order.credit_notes.create(gateway: 'Colppy', suborder: self)
      order.credit_notes.last.generate!(order, self) if order.credit_notes.any?
    end

    def generate_coupon(reason)
      Mkp::Coupon::Network.create(
        type: 'Mkp::Coupon::Network',
        code: "#{id}#{Mkp::Coupon::Network.random_coupon}",
        description: "Cupon por cancelación de subsorden #{id} - Motivo: #{reason}",
        store_id: store.id,
        policy: 'value',
        minimum_value: 0,
        amount: total,
        total_available: 1,
        starts_at: Time.zone.now,
        expires_at: Time.zone.now + 90.days,
        apply_on_sale: 1,
        network: 'AR'
      )
    end

    def is_the_network_fulfiller?
      network_setup = Network[network]
      network_setup.shops_can_be_fulfilled? && shop_id == network_setup.default_shop_id
    end

    def set_purchase_id
      begin
        self.purchase_id = SecureRandom.urlsafe_base64(nil, false)
      end while self.class.exists?(purchase_id: purchase_id)
    end

  end
end
