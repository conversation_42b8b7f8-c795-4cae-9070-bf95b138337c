module Mkp
  module Attachment
    class ProductPicture < Picture
      has_attached_file :photo,
        styles: ->(a) { a.instance.class.styles },
        path: "avenida/products/:attachment/:public_folder/:token_:style.:extension",
        processors: [:thumbnail, :paperclip_optimizer],
        default_style: :st,
        convert_options: {all: '-sampling-factor 4:2:0 -strip -quality 80 -colorspace sRGB'}

      has_many :variants,
        class_name: '::Mkp::Variant',
        foreign_key: :picture_id,
        dependent: :nullify

      belongs_to :variant

      has_one :external_object,
        class_name: '::Mkp::Integration::Object',
        as: :integrable,
        dependent: :destroy

      default_scope { order('view_order ASC') }



      def url(style = :l)
        return 'https://tuquejasuma.com/media/cache/e1/4e/e14e65077c992b9b31a3922d55f71095.jpg'if Rails.env.development?
        if processing
          style = :m unless style == :original
          url = unpublished_photo(style, :url)
          "https://#{HOSTNAME}#{url}"
        else
          photo.url(style)
        end
      end

      def destroy
        # PROBLEMA: La falta de contexto no permite decidir correctamente si se puede o no borrar una imagen.
        # Esto se debe a que si estoy tratando de borrar un producto la imagen nunca se entera de ese contexto y no permite el borrado
        # de la ultima imagen asociada a un producto.

        # actualizo el product_id para desvincular la imagen del producto y simular que se eliminó
        # Nunca destruyo las imagenes. Se destruiran con una tarea que revisara que imagenes no tienen un producto asociado.
        # TODO: cron job que elimine las imágenes sin producto cada cierto tiempo
        product.variants.each(&:hide)
        self.update_attribute(:product_id, nil)
        true
      end


      private

      def should_be_destroyed?
        product.blank? || (not last_picture?)
      end

      def last_picture?
        product.pictures.count <= 1
      end

    end
  end
end
