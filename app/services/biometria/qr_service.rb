require 'net/http'
require 'uri'

class QrService
  BASE_URL = QR_BASE_URL

  def initialize
    @client_id = QR_CLIENT_ID
    @client_secret = QR_CLIENT_SECRET
    @provision_key = QR_PROVISION_KEY
    @apikey = QR_API_KEY
    @qr_front_decision_base_url = QR_FRONT_DECISION_BASE_URL
  end

  # Paso 1: Obtener access_token
  def fetch_access_token
    uri = URI.parse("#{BASE_URL}/v1/services/processServices/oauth2/token")
    request = Net::HTTP::Post.new(uri)
    request.content_type = 'application/json'
    request.body = {
      client_id: @client_id,
      client_secret: @client_secret,
      grant_type: 'password',
      provision_key: @provision_key,
      authenticated_userid: 'nssaint'
    }.to_json

    response = execute_request(uri, request)
    response['access_token']
  end

  # Paso 2: Validar token y obtener tokenId
  def validate_token(access_token)
    uri = URI.parse("#{QR_BASE_URL}/v1/services/processServices/oauth2/token")
    request = Net::HTTP::Post.new(uri)
    request['Authorization'] = "Bearer #{access_token}"

    response = execute_request(uri, request)
    response['tokenId']
  end

  # Paso 3: Obtener sessionIDKey
  def get_session_id_key(access_token)
    # para STG
    uri = URI.parse("#{BASE_URL}/v1/scsvc/getSessionIDKey?jwt=#{QR_JWT}")
    # para prod
    #uri = URI.parse("#{BASE_URL}/v1/scsvc/getSessionIDKey?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ2a1l5RDZ2SzN1eThTYlhYNWJJVXd5aElFS2tMWUJsVCJ9.tVRTu6GN9cxJ-61RTwIDMHk9_h6cqYxuNazzA45HZXA")
    request = Net::HTTP::Post.new(uri)
    request['Authorization'] = "Bearer #{access_token}"
    request.content_type = 'application/json'
    request.body = {
      apikey: @apikey,
      sessionidauth: QR_SESSION_ID_AUTH,
      responseinf: QR_RESPONSE_INF
    }.to_json

    response = execute_request(uri, request)
    response['tokenId']
  end

  def get_form_data_for_qr(token_id:, dni:, sexo:, externaltxid:)
    sexo = sexo == 'masculino' ? 'M' : 'F'
    return {
      url: URI.parse("#{QR_BASE_URL_QR_COMPONENT}/qrcomponent").to_s,
      idapp: QR_ID_APP,
      seckey: token_id, # Aquí usamos el token_id obtenido
      country: QR_COUNTRY,
      idmaqc_service: QR_IDMAQC_SERVICE,
      profile_services: QR_PROFILE_SERVICES,
      services: QR_SERVICES,
      externaltxid: externaltxid,
      dni: dni,
      sexo: sexo
    }
  end

  # Paso 4: Generar el QR
  def request_qr(token_id:, dni:, sexo:, externaltxid:)
    uri = URI.parse("#{QR_BASE_URL_QR_COMPONENT}/qrcomponent")
    puts uri
    request = Net::HTTP::Post.new(uri)
    request.set_form_data({
      idapp: QR_ID_APP,
      seckey: token_id, # Aquí usamos el token_id obtenido
      country: QR_COUNTRY,
      idmaqc_service: QR_IDMAQC_SERVICE,
      profile_services: QR_PROFILE_SERVICES,
      services: QR_SERVICES,
      externaltxid: QR_RESPONSE_INF,
      dni: dni,
      sexo: sexo
    })

    response = execute_request(uri, request)
    response.body # Retornar HTML del QR
  end

  def get_data_call(access_token:, idtx:)
    uri = URI.parse("#{BASE_URL}/v1/services/getData")
    request = Net::HTTP::Post.new(uri)
    request['Authorization'] = "Bearer #{access_token}"
    request.content_type = 'application/json'
    request.body = {
      apikey: @apikey,
      seckey: '1234',
      idtx: idtx
    }.to_json

    response = execute_request(uri, request)
    response
  end

  def get_data_from_idtx(idtx: )
    service = QrService.new
    token = service.fetch_access_token
    service.get_data_call(access_token: token, idtx: idtx)
  end

  def get_decision_from_idtx(idtx: )
    service = QrService.new
    response = service.get_data_from_idtx(idtx: idtx)
    response['decision']
  end

  private

  def execute_request(uri, request)
    Net::HTTP.start(uri.hostname, uri.port, use_ssl: uri.scheme == 'https') do |http|
      response = http.request(request)
      case response
      when Net::HTTPSuccess
        JSON.parse(response.body)
      else
        raise StandardError, "HTTP Error: #{response.body}"
      end
    end
  end
end