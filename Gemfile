source 'https://rubygems.org'

ruby '2.6.7'

Dir.glob(File.expand_path("../engines/*", __FILE__)).each do |path|
  gem File.basename(path), path: path
end

git_source(:github) {|repo_name| "https://github.com/goodpeople" }
gem 'colppy'
gem 'listable', git: '**************:goodpeople/listable.git'
gem 'mercadolibre', git: '**************:goodpeople/mercadolibre.git'
gem 'mercadopago', git: '**************:goodpeople/mercadopago.git', branch: 'master'
gem 'oca-epak', git: '**************:goodpeople/oca-epak.git', branch: 'add_ingreso_or_multiples_retiros'
gem 'omniauth-mercadolibre', git: '**************:goodpeople/omniauth-mercadolibre-ar.git', branch: 'rails-5'
gem 'omniauth-shopify-oauth2'
gem 'paperclip-optimizer'
gem 'sidekiq_status'
gem 'sidekiq-failures'
gem 'avenida-payments', git: '**************:avenida-maas/avenida-payments.git', branch: 'bna_master', ref: '9db4d81'
gem 'avenida_wrappers', git: '*******************:backend/platform/avenida-wrappers.git', branch: 'main'
gem 'avenida_aws', git: '*******************:backend/avenida-plugins/avenida-aws.git', branch: 'main'

# generating the minimal number of SQL insert statements required
gem 'activerecord-import', '~> 1.1.0'
# sorting and reordering a number of objects in a list
gem 'acts_as_list', '~> 1.0.4'
# allow synchronization with server-side pagination
gem 'ajax-datatables-rails', '~> 0.4.3'
# scientific conversion library
gem 'alchemist', '~> 0.1.8'
# tree structure allowing all of them to be fetched in a single SQL query
gem 'ancestry', '~> 3.2.1'
# api requests
gem 'async_request'
# ruby authentication solution
gem 'authlogic', '~> 4.5.0'
# collection of filters for transforming text into HTML code
gem 'auto_html', '~> 1.6.4'
# API clients for AWS
gem 'aws-sdk', '~> 1.61.0'
# amazon Simple Storage Service
gem 'aws-sdk-s3', '~> 1.96.1'
# generates barcodes in a variety of symbologies
gem 'barby', '~> 0.6.8', require: false
# gem 'bcrypt', '~> 3.1.16' # simple wrapper for safely handling passwords
gem 'browser', git: '**************:mjlescano/browser.git'
# global payments platform
gem 'braintree', '~> 4.2.0'
# tracking and installing the exact gems and versions that are needed
gem 'bundler', '~> 2.0.0.pre.3'
# restricts what resources a given user is allowed to access
gem 'cancancan', '~> 3.3.0'
#chart.js for use in Rails asset pipeline
gem 'chart-js-rails', '~> 0.1.7'
# read and write PNG images
gem 'chunky_png', '~> 1.4.0', require: false
# makes it easier to handle nested forms
gem 'cocoon', '~> 1.2.15'
# coffeeScript adapter
gem 'coffee-rails', '~> 4.1.1'
# to make it easy to access a credit card's brand, issuer, type and more
gem 'credit_card_bins', '~> 0.0.3'
# authentication solution
gem 'devise', '~> 4.8.0'
# draper adds an object-oriented layer of presentation logic
gem 'draper', '~> 1.4.0'
# simple shipping API
gem 'easypost', '~> 3.3.0'
# sending notifications when errors occur
# gem 'exception_notification', '~> 4.2.2'
# module to read EXIF from JPEG and TIFF images
gem 'exifr', '~> 1.3.9'
gem 'faraday', '~> 0.9.2'
# determining if a string is blank
gem 'fast_blank', '~> 1.0.0'
# facebook Graph API wrapper
gem 'fb_graph', '~> 2.7.17'
# plugin that makes it easy to filter, search, and sort
gem 'filterrific', '~> 4.0.1'
# [As stated in their Docs] In Rails 3.2, make sure font-awesome-rails is outside the bundler asset group so that these helpers are automatically loaded in production environments.
gem 'font-awesome-rails', '~> *******'
# Form builder, flash message
gem 'foundation_rails_helper', '~> 4.0.0'
# create pretty URLs and work with human-friendly strings as if they were numeric ids
gem 'friendly_id', '~> 5.4.2'
# fuzzy matching library for Ruby strings and arrays of strings
gem 'fuzzy_match'
# object geocoding
gem 'geocoder', '~> 1.6.7'
# searches a GeoIP database for a given host or IP address
gem 'geoip', '~> 1.6.4'
# an API wrapper for MailChimp's
gem 'gibbon', '~> 3.2.0'
# HTML Abstraction Markup Language
gem 'haml', '~> 5.2.1'
# library to interact with HipChat
gem 'hipchat', '~> 1.6.0'
# provide the Rails I18n translations on the JavaScript
gem 'i18n-js', '~> 3.0.0.rc9'
# provides Rails support for the Interactor gem
gem 'interactor-rails', '~> 2.1', '>= 2.1.1'
# precompiled binaries for image_optim
gem 'image_optim_pack', '~> 0.7.0.20210511'
# basic methods to access the Issuu API
gem 'issuu', '~> 0.3.0'
# simple DSL for declaring JSON structures
gem 'jbuilder', '~> 2.9.1'
# provides jQuery
gem 'jquery-rails', '~> 4.4.0'
# implementation of the RFC 7519 OAuth JSON Web Token (JWT) standard
gem 'jwt', '~> 2.2.3'
# create models for metrics and dimensions
gem 'legato', '~> 0.7.0'
# listens to file modifications and notifies you about the changes
gem 'listen', '~> 3.1.1'

gem 'mail_view', git: '**************:benoist/mail_view.git'
# automating interaction with websites
gem 'mechanize', '~> 2.8.1'
# database
gem 'mysql2', '~> 0.5.3'
# implementation of the SCP client protocol
gem 'net-scp', '~> 3.0.0'
# implementation of the SFTP client protocol
gem 'net-sftp', '~> 3.0.0'
# username/password based authentication system for OmniAuth
gem 'oj', '~> 3.14', '>= 3.14.2'
gem 'omniauth-identity', '~> 3.0.9'
# OpenURI patch to allow redirections between HTTP and HTTPS
gem 'open_uri_redirections', '~> 0.2.1'
# simple, fast, threaded, and highly concurrent HTTP 1.1 server for Ruby/Rack applications
gem 'puma', '~> 5.3.2'
# working with color palettes
gem 'paleta', '~> 0.2.1'
# DEPRECATED easy upload management for ActiveRecord
gem 'paperclip', '~> 6.1.0'
# track changes to your models, for auditing or versioning
gem 'paper_trail', '~> 10.3.1'
# give people feedback about long-running tasks without overloading them with information
gem 'progress_bar', '~> 1.3.3'
# Ruby API Builder Language
gem 'rabl', '~> 0.14.5'
# rack middleware for throttling and blocking abusive requests
gem 'rack-attack', '~> 6.5.0'
gem 'rack-cors', '~> 1.0.6', require: 'rack/cors'
# useful for testing or in cases where webserver configuration is unavailable.
gem 'rack-reverse-proxy', '~> 0.12.0', require: 'rack/reverse_proxy'
gem 'rails', '********'
# split your routes in multiple files
gem 'rails_routes_drawer', '~> 0.1.1'
# tries to match Redis' API one-to-one, while still providing an idiomatic interface
gem 'redis', '~> 3.3.5'
gem 'redis-namespace'
# redis for rails
gem 'redis-rails', '5.0.2'
# set of responders modules to dry up your Rails app
gem 'responders', '~> 2.4.1'
# interface between Ruby and ImageMagick
gem 'rmagick', '~> 2.13.4', require: false
# sending HTML emails a little less painful by inlining stylesheets and rewriting relative URLs
gem 'roadie', '~> 4.0.0'
# assign roles to a model
gem 'role_model', '~> 0.8.2'
# Easy and powerful exception tracking for Ruby
gem 'rollbar', '~> 3.3'
# can access the contents of various spreadsheet files
gem 'roo', '~> 2.8.3'
# library for encoding QR Codes
gem 'rqrcode', '~> 2.0.0'
# generate beautiful API documentation
gem 'rswag', '~> 1.5.2'
# adapter for the Rails asset pipeline
gem 'sass-rails', '~> 6.0.0'
# SOAP client
gem 'savon', '~> 2.12.1'
# adds methods to ActiveRecord::Migration to create and manage database views in Rails
gem 'scenic', '~> 1.5.4'
# MySQL adapter for thoughtbot/scenic
gem 'scenic-mysql_adapter', '~> 1.0.1'
# generator html with javascript search index
gem 'sdoc', '~> 0.4.2', group: :doc
# to Interact with Twilio SendGrids API in native Ruby
gem 'sendgrid-ruby', '~> 6.4.0'
# access the admin section of Shopify stores
gem 'shopify_api', '~> 9.4.1'
# simple, efficient background processing for Ruby
gem 'sidekiq', '< 5'
# Google's OAuth 1.0 / OAuth 2.0 implementation to use it with legato gem
gem 'signet', '~> 0.12.0', require: false
# provides enum-like fields for ActiveRecord
gem 'simple_enum', '~> 1.6'
# simply builds and verifies OAuth headers
gem 'simple_oauth', '~> 0.2.0'
# reduce the syntax to the essential parts without becoming cryptic
gem 'slim', '~> 4.1.0'
# fast autocomplete feature
gem 'soulmate', '~> 1.1.0'
# library for compiling and serving web assets
gem 'sprockets-rails', '~> 2.3.3'
# Ruby Google Analytics Measurement
gem 'staccato', '~> 0.5.3'
# integration between Sunspot and ActiveRecord
gem 'sunspot_rails', '~> 2.5.0'
# provides a bundled Solr distribution for use with Sunspot
gem 'sunspot_solr', '~> 2.5.0'
# unit testing framework
gem 'test-unit', '~> 3.4.4'
# makes navigating your web application faster
gem 'turbolinks', '~> 5.2.1'
# runs HTTP requests in parallel while cleanly encapsulating handling logic
gem 'typhoeus', '~> 1.4.0'
# minifies JavaScript files by wrapping UglifyJS to be accessible in Ruby
gem 'uglifier', '~> 4.2.0'
# skip default_scope in your associations
gem 'unscoped_associations', '~> 0.7.1'
# simple universally unique ID generation library
gem 'uuidtools', '~> 2.2.0', require: false
# clean ruby syntax for writing and deploying cron jobs
gem 'whenever', '~> 1.0.0', require: false
# function backport
gem 'where-or', '~> 0.1.6'
# serve a PDF file to a user from HTML
gem 'wicked_pdf', '~> 2.1.0'
# provides a simple API for performing paginated queries with Active Record
gem 'will_paginate', '~> 3.0.0'
# provides binaries for WKHTMLTOPDF project in an easily accessible package
gem 'wkhtmltopdf-binary', '~> ********'
# Better object view
gem "awesome_print"

# sentry
gem "sentry-raven"

# New Relic
gem 'newrelic_rpm'

# PDF generator
gem 'prawn'

group :assets do
  # a Sass-powered version of Bootstrap 3
  gem 'bootstrap-sass', '~> 3.4.1'
  # library of pure Sass mixins and functions
  gem 'bourbon', '~> 4.0.0'
  # Ruby Wrapper for the Google Closure Compiler
  gem 'closure-compiler', '~> 1.1.14'
  # jquery datatables for rails
  gem 'jquery-datatables-rails', '~> 3.4.0'
  # a jQuery based replacement for select boxes
  gem 'select2-rails', '4.0.2'
end

group :development do
  # An iterations per second enhancement to Benchmark
  gem 'benchmark-ips', '~> 2.9.1', require: false
  # a better error page for Rails and other Rack apps
  gem 'better_errors', '~> 2.9.1'
  # grab bindings from higher up the call stack and evaluate code in that context
  gem 'binding_of_caller', '~> 1.0.0'
  # kill N+1 queries
  gem 'bullet'
  # Puma integration for Capistrano 3
  gem 'capistrano3-puma', '~> 5.0.4',   require: false
  # utility and framework for executing commands in parallel on multiple remote machines, via SSH
  gem 'capistrano', '~> 3.16.0',        require: false
  # bundler support for Capistrano 3
  gem 'capistrano-bundler', '~> 2.0.1', require: false
  # RVM integration for Capistrano
  gem 'capistrano-rvm', '~> 0.1.2',     require: false
  # Rails specific Capistrano tasks
  gem 'capistrano-rails', '~> 1.6.1',   require: false
  # Raises an error when you use a capybara finder and it times out
  gem 'capybara-slow_finder_errors', '~> 0.1.5'
  # Run Rake/Rails commands through the console
  gem 'commands', '~> 0.2.1'
  # makes using vector icons easy
  gem 'fontcustom', '~> 2.0.0', require: false
  # keep an eye on every Net::HTTP library usage
  gem 'http_logger', '~> 0.5.1'
  # supporting gem for Rails Panel (Google Chrome extension for Rails development)
  gem 'meta_request', '~> 0.7.2'
  # turns off the Rails asset pipeline log
  gem 'quiet_assets', '~> 1.1.0'
  # Ruby code style checking and code formatting tool
  gem 'rubocop', '~> 1.17.0'
  # A collection of RuboCop cops to check for performance optimizations in Ruby code
  gem 'rubocop-performance', '~> 1.11.3'
  # Automatic Rails code style checking tool
  gem 'rubocop-rails', '~> 2.11.0'
  # A plugin for the RuboCop code style enforcing & linting tool.
  gem 'rubocop-rspec', '~> 2.4.0'
  # tool for writing clean and consistent SCSS
  gem 'scss_lint', '~> 0.59.0', require: false
  # Preloads your application so things like console, rake and tests run faster
  gem 'spring', '~> 2.1.1'
  # A thin and fast web server
  gem 'thin', '~> 1.8.1'
  # debugging tool
  gem 'web-console', '~> 2.3.0'
end

group :development, :test do
  # gem 'awesome_rails_console' Commented for testing purpose  gem 'byebug'
  # Ruby debugger
  gem 'byebug', '~> 11.1.3'
  # controlling external programs running in the background on any Ruby / OS combination
  gem 'childprocess', '~> 4.1.0'
  # NOTE: This gem runs over root path of app  with fasterer command bundle exec fasterer.
  gem 'fasterer', '~> 0.9.0'
  # generates dummy data
  gem 'ffaker', '~> 2.18.0'
  # support for arbitrary Ruby apps
  gem 'flamegraph', '~> 0.9.5'
  # find and manage missing and unused translations
  gem 'i18n-tasks', '~> 0.7.13'
  # helper class for launching cross-platform applications
  gem 'launchy', '~> 2.5.0'
  # open a preview in the browser instead of sending
  gem 'letter_opener', '~> 1.7.0'
  # memory profiling routines
  gem 'memory_profiler', '~> 1.0.0'
  # Profiling toolkit for Rack applications with Rails integration
  gem 'rack-mini-profiler', '~> 2.3.2', require: false
  # FSEvents API with Signals catching (without RubyCocoa)
  gem 'rb-fsevent', '~> 0.11.0', require: false
  # testing framework
  gem 'rspec-rails', '~> 3.9.1'
  #  a fast sampling profiler for ruby code
  gem 'stackprof', '~> 0.2.17'
end

group :production do
  # Access to additional services for Engine Yard customers.
  gem 'ey_config', '~> 0.0.7'
  #  nicorn is an HTTP server for Rack applications designed to only serve fast clients on low-latency, high-bandwidth connections and take advantage of features in Unix/Unix-like kernels.
  gem 'unicorn', '~> 6.0.0'
end

group :test do
  # For cleaning test DB when run rspec.
  gem 'database_cleaner', '~> 1.99.0'
  # provides Rails integration for factory_bot
  gem 'factory_bot_rails', '~> 5.2.0'
  # Faker, a port of Data::Faker from Perl, is used to easily generate fake data: names, addresses, phone numbers, etc.
  gem 'faker', '~> 2.2.1'
  # Shoulda Matchers provides RSpec- and Minitest-compatible one-liners to test common Rails functionality
  gem 'shoulda-matchers', '~> 4.5'
  gem 'simplecov', '~> 0.21.2', require: false
  # WebMock allows stubbing HTTP requests and setting expectations on HTTP requests.
  gem 'webmock', '~> 3.13.0'
end
